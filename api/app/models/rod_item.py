"""
路亚竿型号模型
对应数据库中的 rod_item 表
"""

from typing import Any
from decimal import Decimal
from sqlmodel import SQLModel, Field, JSON, Column
from app.db.base import BaseModel


class RodItem(BaseModel, table=True):
    """路亚竿型号表"""
    __tablename__ = "rod_item"

    id: int | None = Field(default=None, primary_key=True, description="主键ID")
    uuid: str = Field(max_length=64, description="UUID")
    rod_id: int = Field(foreign_key="rod.id", description="产品ID")
    name: str = Field(max_length=30, description="型号名称")
    sub_name: str | None = Field(default=None, max_length=30, description="子名称")
    total_length: dict[str, Any] | None = Field(default=None, sa_column=Column(JSON), description="总长")
    pieces: int | None = Field(default=None, description="节数")
    min_lure_wt: Decimal | None = Field(default=None, max_digits=3, decimal_places=1, description="最小饵重")
    max_lure_wt: Decimal | None = Field(default=None, max_digits=3, decimal_places=1, description="最大饵重")
    min_line_wt: int | None = Field(default=None, description="最小线拉力")
    max_line_wt: int | None = Field(default=None, description="最大线拉力")
    min_line_pe: Decimal | None = Field(default=None, max_digits=2, decimal_places=1, description="最小PE线号")
    max_line_pe: Decimal | None = Field(default=None, max_digits=2, decimal_places=1, description="最大PE线号")
    weight: float | None = Field(default=None, description="重量")
    action: str | None = Field(default=None, max_length=20, description="调性")
    power: str | None = Field(default=None, max_length=30, description="硬度")
    grip_material: str | None = Field(default=None, max_length=255, description="手把材料")
    grip_length: Decimal | None = Field(default=None, max_digits=3, decimal_places=1, description="手把长度")
    reel_seat_type: str | None = Field(default=None, max_length=20, description="枪柄还是直柄")
    cover: str | None = Field(default=None, max_length=255, description="型号图片")
    desc: str | None = Field(default=None, description="型号说明")
    short_desc: str | None = Field(default=None, max_length=50, description="一句话描述")
    release_year: int | None = Field(default=None, description="发售年份")


class RodItemCreate(SQLModel):
    """创建路亚竿型号时的数据模型"""
    uuid: str = Field(max_length=64, description="UUID")
    rod_id: int = Field(description="产品ID")
    name: str = Field(max_length=30, description="型号名称")
    sub_name: str | None = Field(default=None, max_length=30, description="子名称")
    total_length: dict[str, Any] | None = Field(default=None, description="总长")
    pieces: int | None = Field(default=None, description="节数")
    min_lure_wt: Decimal | None = Field(default=None, description="最小饵重")
    max_lure_wt: Decimal | None = Field(default=None, description="最大饵重")
    min_line_wt: int | None = Field(default=None, description="最小线拉力")
    max_line_wt: int | None = Field(default=None, description="最大线拉力")
    min_line_pe: Decimal | None = Field(default=None, description="最小PE线号")
    max_line_pe: Decimal | None = Field(default=None, description="最大PE线号")
    weight: float | None = Field(default=None, description="重量")
    action: str | None = Field(default=None, max_length=20, description="调性")
    power: str | None = Field(default=None, max_length=30, description="硬度")
    grip_material: str | None = Field(default=None, max_length=255, description="手把材料")
    grip_length: Decimal | None = Field(default=None, description="手把长度")
    reel_seat_type: str | None = Field(default=None, max_length=20, description="枪柄还是直柄")
    cover: str | None = Field(default=None, max_length=255, description="型号图片")
    desc: str | None = Field(default=None, description="型号说明")
    short_desc: str | None = Field(default=None, max_length=50, description="一句话描述")
    release_year: int | None = Field(default=None, description="发售年份")


class RodItemUpdate(SQLModel):
    """更新路亚竿型号时的数据模型"""
    uuid: str | None = Field(default=None, max_length=64, description="UUID")
    rod_id: int | None = Field(default=None, description="产品ID")
    name: str | None = Field(default=None, max_length=30, description="型号名称")
    sub_name: str | None = Field(default=None, max_length=30, description="子名称")
    total_length: dict[str, Any] | None = Field(default=None, description="总长")
    pieces: int | None = Field(default=None, description="节数")
    min_lure_wt: Decimal | None = Field(default=None, description="最小饵重")
    max_lure_wt: Decimal | None = Field(default=None, description="最大饵重")
    min_line_wt: int | None = Field(default=None, description="最小线拉力")
    max_line_wt: int | None = Field(default=None, description="最大线拉力")
    min_line_pe: Decimal | None = Field(default=None, description="最小PE线号")
    max_line_pe: Decimal | None = Field(default=None, description="最大PE线号")
    weight: float | None = Field(default=None, description="重量")
    action: str | None = Field(default=None, max_length=20, description="调性")
    power: str | None = Field(default=None, max_length=30, description="硬度")
    grip_material: str | None = Field(default=None, max_length=255, description="手把材料")
    grip_length: Decimal | None = Field(default=None, description="手把长度")
    reel_seat_type: str | None = Field(default=None, max_length=20, description="枪柄还是直柄")
    cover: str | None = Field(default=None, max_length=255, description="型号图片")
    desc: str | None = Field(default=None, description="型号说明")
    short_desc: str | None = Field(default=None, max_length=50, description="一句话描述")
    release_year: int | None = Field(default=None, description="发售年份")


class RodItemRead(SQLModel):
    """读取路亚竿型号时的数据模型"""
    id: int
    uuid: str
    rod_id: int
    name: str
    sub_name: str | None = None
    total_length: dict[str, Any] | None = None
    pieces: int | None = None
    min_lure_wt: Decimal | None = None
    max_lure_wt: Decimal | None = None
    min_line_wt: int | None = None
    max_line_wt: int | None = None
    min_line_pe: Decimal | None = None
    max_line_pe: Decimal | None = None
    weight: float | None = None
    action: str | None = None
    power: str | None = None
    grip_material: str | None = None
    grip_length: Decimal | None = None
    reel_seat_type: str | None = None
    cover: str | None = None
    desc: str | None = None
    short_desc: str | None = None
    release_year: int | None = None
    created_at: int | None = None
    updated_at: int | None = None
