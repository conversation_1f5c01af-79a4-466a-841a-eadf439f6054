"""
用户模型
对应数据库中的 user 表
"""

from sqlmodel import SQLModel, Field


class User(SQLModel, table=True):
    """用户账号表"""
    __tablename__ = "user"

    id: int | None = Field(default=None, primary_key=True, description="主键ID")
    username: str = Field(max_length=64, unique=True, description="账号")
    screen_name: str | None = Field(default=None, max_length=30, description="昵称")
    auth_key: str = Field(max_length=32, description="认证key")
    password_hash: str = Field(max_length=255, description="加密密码")
    password_reset_token: str | None = Field(default=None, max_length=255, unique=True, description="密码重置key")
    phone: str | None = Field(default=None, max_length=20, unique=True, description="手机号")
    email: str | None = Field(default=None, max_length=255, unique=True, description="邮箱")
    status: int = Field(description="用户状态，1启用，2禁用")
    created_at: int = Field(description="创建时间")
    updated_at: int = Field(description="更新时间")
    verification_token: str | None = Field(default=None, max_length=255, description="确认key")


class UserCreate(SQLModel):
    """创建用户时的数据模型"""
    username: str = Field(max_length=64, description="账号")
    screen_name: str | None = Field(default=None, max_length=30, description="昵称")
    password: str = Field(min_length=6, description="密码")
    phone: str | None = Field(default=None, max_length=20, description="手机号")
    email: str | None = Field(default=None, max_length=255, description="邮箱")


class UserUpdate(SQLModel):
    """更新用户时的数据模型"""
    screen_name: str | None = Field(default=None, max_length=30, description="昵称")
    phone: str | None = Field(default=None, max_length=20, description="手机号")
    email: str | None = Field(default=None, max_length=255, description="邮箱")
    status: int | None = Field(default=None, description="用户状态")


class UserRead(SQLModel):
    """读取用户时的数据模型"""
    id: int
    username: str
    screen_name: str | None = None
    phone: str | None = None
    email: str | None = None
    status: int
    created_at: int
    updated_at: int


class UserLogin(SQLModel):
    """用户登录数据模型"""
    username: str = Field(description="用户名或邮箱或手机号")
    password: str = Field(description="密码")
