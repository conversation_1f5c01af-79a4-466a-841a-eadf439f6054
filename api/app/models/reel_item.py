"""
渔轮型号模型
对应数据库中的 reel_item 表
"""

from typing import Any
from decimal import Decimal
from sqlmodel import SQLModel, Field, JSON, Column
from app.db.base import BaseModel


class ReelItem(BaseModel, table=True):
    """渔轮型号表"""
    __tablename__ = "reel_item"

    id: int | None = Field(default=None, primary_key=True, description="主键ID")
    uuid: str = Field(max_length=64, description="UUID")
    reel_id: int = Field(foreign_key="reel.id", description="渔轮ID")
    name: str = Field(max_length=30, description="型号名称")
    gear_ratio: Decimal | None = Field(default=None, max_digits=2, decimal_places=1, description="速比")
    cover: str | None = Field(default=None, max_length=255, description="型号图片")
    desc: str | None = Field(default=None, description="型号说明")
    short_desc: str | None = Field(default=None, max_length=50, description="一句话描述")
    best_drag: str | None = Field(default=None, max_length=255, description="实用卸力kg")
    max_drag: Decimal | None = Field(default=None, max_digits=3, decimal_places=1, description="最大卸力kg")
    weight: int | None = Field(default=None, description="重量g")
    spool: str | None = Field(default=None, max_length=30, description="线杯参数")
    bearings: str | None = Field(default=None, max_length=10, description="轴承数")
    nylon_capacity: dict[str, Any] | None = Field(default=None, sa_column=Column(JSON), description="尼龙线容量")
    pe_capacity: dict[str, Any] | None = Field(default=None, sa_column=Column(JSON), description="PE线容量")
    fluorocarbon_capacity: dict[str, Any] | None = Field(default=None, sa_column=Column(JSON), description="碳氟线容量")
    line_per_crank: int | None = Field(default=None, description="手把转一圈最大收线长")
    handle_length: int | None = Field(default=None, description="摇臂长度")
    handle_position: str | None = Field(default=None, max_length=10, description="摇臂位置LEFT/RIGHT")
    release_year: int | None = Field(default=None, description="发售日期")


class ReelItemCreate(SQLModel):
    """创建渔轮型号时的数据模型"""
    uuid: str = Field(max_length=64, description="UUID")
    reel_id: int = Field(description="渔轮ID")
    name: str = Field(max_length=30, description="型号名称")
    gear_ratio: Decimal | None = Field(default=None, description="速比")
    cover: str | None = Field(default=None, max_length=255, description="型号图片")
    desc: str | None = Field(default=None, description="型号说明")
    short_desc: str | None = Field(default=None, max_length=50, description="一句话描述")
    best_drag: str | None = Field(default=None, max_length=255, description="实用卸力kg")
    max_drag: Decimal | None = Field(default=None, description="最大卸力kg")
    weight: int | None = Field(default=None, description="重量g")
    spool: str | None = Field(default=None, max_length=30, description="线杯参数")
    bearings: str | None = Field(default=None, max_length=10, description="轴承数")
    nylon_capacity: dict[str, Any] | None = Field(default=None, description="尼龙线容量")
    pe_capacity: dict[str, Any] | None = Field(default=None, description="PE线容量")
    fluorocarbon_capacity: dict[str, Any] | None = Field(default=None, description="碳氟线容量")
    line_per_crank: int | None = Field(default=None, description="手把转一圈最大收线长")
    handle_length: int | None = Field(default=None, description="摇臂长度")
    handle_position: str | None = Field(default=None, max_length=10, description="摇臂位置LEFT/RIGHT")
    release_year: int | None = Field(default=None, description="发售日期")


class ReelItemUpdate(SQLModel):
    """更新渔轮型号时的数据模型"""
    uuid: str | None = Field(default=None, max_length=64, description="UUID")
    reel_id: int | None = Field(default=None, description="渔轮ID")
    name: str | None = Field(default=None, max_length=30, description="型号名称")
    gear_ratio: Decimal | None = Field(default=None, description="速比")
    cover: str | None = Field(default=None, max_length=255, description="型号图片")
    desc: str | None = Field(default=None, description="型号说明")
    short_desc: str | None = Field(default=None, max_length=50, description="一句话描述")
    best_drag: str | None = Field(default=None, max_length=255, description="实用卸力kg")
    max_drag: Decimal | None = Field(default=None, description="最大卸力kg")
    weight: int | None = Field(default=None, description="重量g")
    spool: str | None = Field(default=None, max_length=30, description="线杯参数")
    bearings: str | None = Field(default=None, max_length=10, description="轴承数")
    nylon_capacity: dict[str, Any] | None = Field(default=None, description="尼龙线容量")
    pe_capacity: dict[str, Any] | None = Field(default=None, description="PE线容量")
    fluorocarbon_capacity: dict[str, Any] | None = Field(default=None, description="碳氟线容量")
    line_per_crank: int | None = Field(default=None, description="手把转一圈最大收线长")
    handle_length: int | None = Field(default=None, description="摇臂长度")
    handle_position: str | None = Field(default=None, max_length=10, description="摇臂位置LEFT/RIGHT")
    release_year: int | None = Field(default=None, description="发售日期")


class ReelItemRead(SQLModel):
    """读取渔轮型号时的数据模型"""
    id: int
    uuid: str
    reel_id: int
    name: str
    gear_ratio: Decimal | None = None
    cover: str | None = None
    desc: str | None = None
    short_desc: str | None = None
    best_drag: str | None = None
    max_drag: Decimal | None = None
    weight: int | None = None
    spool: str | None = None
    bearings: str | None = None
    nylon_capacity: dict[str, Any] | None = None
    pe_capacity: dict[str, Any] | None = None
    fluorocarbon_capacity: dict[str, Any] | None = None
    line_per_crank: int | None = None
    handle_length: int | None = None
    handle_position: str | None = None
    release_year: int | None = None
    created_at: int | None = None
    updated_at: int | None = None
