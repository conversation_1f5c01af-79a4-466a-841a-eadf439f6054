"""
鱼种相关的Pydantic模型
用于API请求和响应的数据验证
"""

from pydantic import BaseModel, Field

from app.schemas.common import SearchParams


class FishSearchParams(SearchParams):
    """鱼种搜索参数"""
    keyword: str | None = Field(default=None, description="搜索关键词")


class FishCreate(BaseModel):
    """创建鱼种请求模型"""
    name: str = Field(max_length=30, description="鱼种名称")
    alias_name: str | None = Field(default=None, max_length=255, description="别名")


class FishUpdate(BaseModel):
    """更新鱼种请求模型"""
    name: str | None = Field(default=None, max_length=30, description="鱼种名称")
    alias_name: str | None = Field(default=None, max_length=255, description="别名")


class FishResponse(BaseModel):
    """鱼种响应模型"""
    id: int = Field(description="鱼种ID")
    name: str = Field(description="鱼种名称")
    alias_name: str | None = Field(default=None, description="别名")


class FishListItem(BaseModel):
    """鱼种列表项模型"""
    id: int = Field(description="鱼种ID")
    name: str = Field(description="鱼种名称")
    alias_name: str | None = Field(default=None, description="别名")


class FishDetailResponse(FishResponse):
    """鱼种详情响应模型"""
    pass  # 目前与基础响应模型相同，预留扩展空间
