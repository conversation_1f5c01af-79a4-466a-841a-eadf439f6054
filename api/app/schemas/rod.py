"""
路亚竿相关的Pydantic模型
用于API请求和响应的数据验证
"""

from typing import Any
from decimal import Decimal
from pydantic import BaseModel, Field
from .common import SearchParams


class RodSearchParams(SearchParams):
    """路亚竿搜索参数"""
    company_id: int | None = Field(default=None, description="品牌ID")
    series_id: int | None = Field(default=None, description="系列ID")
    min_lure_wt: Decimal | None = Field(default=None, description="最小饵重")
    max_lure_wt: Decimal | None = Field(default=None, description="最大饵重")
    action: str | None = Field(default=None, description="调性")
    power: str | None = Field(default=None, description="硬度")
    reel_seat_type: str | None = Field(default=None, description="枪柄/直柄")


class RodResponse(BaseModel):
    """路亚竿响应模型"""
    id: int = Field(description="路亚竿ID")
    uuid: str = Field(description="UUID")
    name: str = Field(description="路亚竿名称")
    company_id: int | None = Field(default=None, description="品牌ID")
    company_name: str | None = Field(default=None, description="品牌名称")
    series_id: int | None = Field(default=None, description="系列ID")
    series_name: str | None = Field(default=None, description="系列名称")
    cover: str | None = Field(default=None, description="封面图片")
    desc: str | None = Field(default=None, description="描述")


class RodItemResponse(BaseModel):
    """路亚竿型号响应模型"""
    id: int = Field(description="型号ID")
    uuid: str = Field(description="UUID")
    rod_id: int = Field(description="路亚竿ID")
    name: str = Field(description="型号名称")
    sub_name: str | None = Field(default=None, description="子名称")
    total_length: dict[str, Any] | None = Field(default=None, description="总长")
    pieces: int | None = Field(default=None, description="节数")
    min_lure_wt: Decimal | None = Field(default=None, description="最小饵重")
    max_lure_wt: Decimal | None = Field(default=None, description="最大饵重")
    min_line_wt: int | None = Field(default=None, description="最小线拉力")
    max_line_wt: int | None = Field(default=None, description="最大线拉力")
    min_line_pe: Decimal | None = Field(default=None, description="最小PE线号")
    max_line_pe: Decimal | None = Field(default=None, description="最大PE线号")
    weight: float | None = Field(default=None, description="重量")
    action: str | None = Field(default=None, description="调性")
    power: str | None = Field(default=None, description="硬度")
    grip_material: str | None = Field(default=None, description="手把材料")
    grip_length: Decimal | None = Field(default=None, description="手把长度")
    reel_seat_type: str | None = Field(default=None, description="枪柄还是直柄")
    cover: str | None = Field(default=None, description="型号图片")
    desc: str | None = Field(default=None, description="型号说明")
    short_desc: str | None = Field(default=None, description="一句话描述")
    release_year: int | None = Field(default=None, description="发售年份")


class RodDetailResponse(RodResponse):
    """路亚竿详情响应模型"""
    items: list[RodItemResponse] = Field(default_factory=list, description="型号列表")


class RodCreate(BaseModel):
    """创建路亚竿请求模型"""
    uuid: str = Field(max_length=64, description="UUID")
    company_id: int | None = Field(default=None, description="厂商ID")
    name: str = Field(max_length=50, description="名称")
    series_id: int | None = Field(default=None, description="品牌系列ID")
    cover: str | None = Field(default=None, max_length=255, description="封面图片")
    desc: str | None = Field(default=None, description="描述")


class RodUpdate(BaseModel):
    """更新路亚竿请求模型"""
    uuid: str | None = Field(default=None, max_length=64, description="UUID")
    company_id: int | None = Field(default=None, description="厂商ID")
    name: str | None = Field(default=None, max_length=50, description="名称")
    series_id: int | None = Field(default=None, description="品牌系列ID")
    cover: str | None = Field(default=None, max_length=255, description="封面图片")
    desc: str | None = Field(default=None, description="描述")


class RodListItem(BaseModel):
    """路亚竿列表项模型"""
    id: int = Field(description="路亚竿ID")
    uuid: str = Field(description="UUID")
    name: str = Field(description="路亚竿名称")
    company_name: str | None = Field(default=None, description="品牌名称")
    series_name: str | None = Field(default=None, description="系列名称")
    cover: str | None = Field(default=None, description="封面图片")
    item_count: int = Field(default=0, description="型号数量")
    price_range: str | None = Field(default=None, description="价格区间")
